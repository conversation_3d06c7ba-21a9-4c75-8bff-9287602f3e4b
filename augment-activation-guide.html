<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment 激活指南</title>
</head>
<body>
    <div class="augment-activation-guide">
      <style>
        .augment-activation-guide {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
        }
        
        .augment-activation-guide .status-banner {
          background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
          color: white;
          padding: 16px 20px;
          border-radius: 8px;
          text-align: center;
          margin-bottom: 24px;
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }
        
        .augment-activation-guide .section-title {
          color: #E53333;
          font-size: 18px;
          font-weight: 600;
          margin: 20px 0 12px 0;
          border-left: 3px solid #E53333;
          padding-left: 12px;
        }
        
        .augment-activation-guide .steps-container {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          margin: 16px 0;
        }
        
        .augment-activation-guide .step {
          display: flex;
          align-items: center;
          margin: 12px 0;
          padding: 12px 16px;
          background: white;
          border-radius: 6px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          transition: all 0.2s ease;
        }
        
        .augment-activation-guide .step:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .augment-activation-guide .step-number {
          background: #6c757d;
          color: white;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
          margin-right: 12px;
          flex-shrink: 0;
        }
        
        .augment-activation-guide .step-important {
          background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
          border: 2px solid #E53333;
          position: relative;
        }
        
        .augment-activation-guide .step-important::before {
          content: '⚠️ 关键步骤';
          position: absolute;
          top: -8px;
          right: 8px;
          background: #E53333;
          color: white;
          padding: 2px 8px;
          font-size: 11px;
          border-radius: 10px;
          font-weight: 600;
        }
        
        .augment-activation-guide .step-important .step-number {
          background: #E53333;
          animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
        
        .augment-activation-guide .highlight-action {
          background: #E53333;
          color: white;
          padding: 3px 8px;
          border-radius: 4px;
          font-weight: 600;
          margin: 0 4px;
          display: inline-block;
        }

        .augment-activation-guide .accept-tag {
          background: #4CAF50;
          color: white;
          padding: 4px 12px;
          border-radius: 4px;
          font-weight: 600;
          margin: 0 4px;
          display: inline-block;
          font-size: 12px;
          letter-spacing: 0.5px;
        }
        
        .augment-activation-guide .video-wrapper {
          margin: 16px 0;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .augment-activation-guide .video-wrapper iframe {
          width: 100%;
          height: 300px;
          border: none;
        }
        
        .augment-activation-guide .info-note {
          background: #e3f2fd;
          border-left: 3px solid #2196F3;
          padding: 12px 16px;
          margin: 16px 0;
          border-radius: 4px;
          font-size: 14px;
        }
        
        .augment-activation-guide .link {
          color: #E53333;
          text-decoration: none;
          font-weight: 600;
        }
        
        .augment-activation-guide .link:hover {
          text-decoration: underline;
        }
      </style>

      <div class="status-banner">
        🎉 系统已稳定运行1个月以上，目前很稳定，成功率100%，可放心使用！
      </div>
      
      <div class="section-title">使用方法：</div>
      
      <div class="steps-container">
        <div class="step">
          <div class="step-number">1</div>
          <div>输入被限制的augment邮箱账号</div>
        </div>
        
        <div class="step">
          <div class="step-number">2</div>
          <div>点击 <strong>立即激活</strong></div>
        </div>
        
        <div class="step step-important">
          <div class="step-number">3</div>
          <div>
            回到augment软件插件界面
            <span class="highlight-action">退出当前账号</span>
            <span class="highlight-action">重新登录</span>
            <span class="accept-tag">ACCEPT</span>
          </div>
        </div>
      </div>
      
      <div class="section-title">视频教程：</div>
      
      <div class="video-wrapper">
        <iframe src="//player.bilibili.com/player.html?isOutside=true&aid=114790164858310&bvid=BV1pQ3gzmECD&cid=30835672913&p=1" allowfullscreen></iframe>
      </div>
      
      <div class="info-note">
        💡 给上面视频三连的朋友可到 
        <a href="https://xoxome.online/?page_id=238" target="_blank" class="link">公众号</a> 
        免费申请激活次数，或右上角在个人中心充值次数。
      </div>
      
      <div class="info-note">
        🔗 <a href="https://xoxome.online/?p=1079" target="_blank" class="link">另一种本地环境续杯方式点我观看</a>
      </div>
    </div>
</body>
</html>
